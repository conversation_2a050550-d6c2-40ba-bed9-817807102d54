# 配置管理和成本统计使用指南 (v0.1.7)

## 📋 概述

TradingAgents-CN v0.1.7 提供了完整的配置管理和成本统计系统，让您可以：

- 🔧 **统一管理API密钥和模型配置**
- 💰 **实时跟踪Token使用和成本**
- 📊 **查看详细的使用统计和趋势**
- ⚙️ **自定义系统设置和费率**
- 🐳 **Docker环境配置管理**
- 📄 **报告导出配置**
- 🧠 **多LLM模型智能路由**

## 🎉 v0.1.7 配置管理新特性

- 🐳 **Docker配置**: 容器化环境的配置管理
- 🧠 **DeepSeek集成**: 成本优化的模型配置
- 📄 **导出配置**: 报告导出功能配置
- 💰 **智能成本控制**: 自动成本监控和限制
- 🔄 **模型路由**: 智能模型选择配置

## 🚀 快速开始

### 1. 启动Web界面

```bash
# 激活虚拟环境
.\env\Scripts\Activate.ps1  # Windows
source env/bin/activate     # Linux/macOS

# 启动Web应用
python -m streamlit run web/app.py
```

### 2. 访问配置管理

1. 在Web界面左侧选择 **"⚙️ 配置管理"**
2. 选择要管理的功能：
   - **模型配置** - 管理API密钥和模型参数
   - **定价设置** - 设置各供应商的费率
   - **使用统计** - 查看Token使用和成本统计
   - **系统设置** - 配置系统默认参数

## 🤖 模型配置管理

### 支持的模型供应商

| 供应商 | 模型示例 | 货币 | 说明 |
|--------|----------|------|------|
| **阿里百炼** | qwen-turbo, qwen-plus-latest, qwen-max | CNY | 国产模型，推荐使用 |
| **OpenAI** | gpt-3.5-turbo, gpt-4, gpt-4-turbo | USD | 国际领先模型 |
| **Google** | gemini-pro, gemini-pro-vision | USD | Google最新模型 |
| **Anthropic** | claude-3-sonnet, claude-3-opus | USD | 高质量对话模型 |

### 配置步骤

#### **添加新模型**

1. 进入 **"模型配置"** 页面
2. 在 **"添加新模型"** 部分填写：
   - **供应商**: 选择模型提供商
   - **模型名称**: 输入具体模型名（如 qwen-plus-latest）
   - **API密钥**: 输入您的API密钥
   - **最大Token数**: 设置输出限制（1000-32000）
   - **温度参数**: 控制输出随机性（0.0-2.0）
   - **启用模型**: 是否在分析中使用

3. 点击 **"添加模型"** 保存

#### **编辑现有模型**

1. 在模型列表中选择要编辑的模型
2. 修改相应参数
3. 点击 **"保存配置"**

### 模型配置示例

```json
{
  "provider": "dashscope",
  "model_name": "qwen-plus-latest",
  "api_key": "sk-your-api-key-here",
  "max_tokens": 4000,
  "temperature": 0.7,
  "enabled": true
}
```

## 💰 定价设置管理

### 默认费率表

#### **阿里百炼 (CNY/1000 tokens)**
| 模型 | 输入价格 | 输出价格 | 说明 |
|------|----------|----------|------|
| qwen-turbo | ¥0.002 | ¥0.006 | 快速响应 |
| qwen-plus | ¥0.004 | ¥0.012 | 平衡性能 |
| qwen-max | ¥0.020 | ¥0.060 | 最强性能 |

#### **OpenAI (USD/1000 tokens)**
| 模型 | 输入价格 | 输出价格 | 说明 |
|------|----------|----------|------|
| gpt-3.5-turbo | $0.0015 | $0.002 | 经济实用 |
| gpt-4 | $0.03 | $0.06 | 强大性能 |
| gpt-4-turbo | $0.01 | $0.03 | 优化版本 |

#### **Google (USD/1000 tokens)**
| 模型 | 输入价格 | 输出价格 | 说明 |
|------|----------|----------|------|
| gemini-pro | $0.00025 | $0.0005 | 高性价比 |
| gemini-pro-vision | $0.00025 | $0.0005 | 支持图像 |

### 自定义定价

1. 进入 **"定价设置"** 页面
2. 选择要编辑的模型定价
3. 修改：
   - **输入价格**: 每1000个输入token的价格
   - **输出价格**: 每1000个输出token的价格
   - **货币**: CNY/USD/EUR
4. 点击 **"保存定价"**

## 📊 使用统计和成本分析

### 统计数据类型

#### **总体统计**
- **总成本**: 指定时间内的总花费
- **总请求数**: API调用次数
- **输入Token**: 总输入token数量
- **输出Token**: 总输出token数量

#### **按供应商统计**
- **各供应商成本分布**
- **使用频率对比**
- **平均成本分析**

#### **使用趋势**
- **每日成本趋势图**
- **每日请求数趋势**
- **成本变化分析**

### 查看统计

1. 进入 **"使用统计"** 页面
2. 选择统计时间范围：
   - 最近7天
   - 最近30天
   - 最近90天
   - 最近365天
3. 查看详细统计图表和数据

### 成本控制

#### **设置成本警告**
1. 进入 **"系统设置"** 页面
2. 设置 **"成本警告阈值"**
3. 当日成本超过阈值时会收到警告

#### **成本优化建议**
- **选择合适的模型**: 根据需求选择性价比最高的模型
- **控制Token使用**: 合理设置最大Token数
- **监控使用趋势**: 定期查看成本统计

## ⚙️ 系统设置

### 基本设置

| 设置项 | 说明 | 默认值 |
|--------|------|--------|
| **默认供应商** | 新分析时的默认模型供应商 | dashscope |
| **默认模型** | 默认使用的模型名称 | qwen-turbo |
| **启用成本跟踪** | 是否记录Token使用和成本 | 启用 |
| **成本警告阈值** | 日成本超过此值时警告 | ¥100 |
| **首选货币** | 成本显示的首选货币 | CNY |
| **自动保存使用记录** | 是否自动保存使用记录 | 启用 |
| **最大使用记录数** | 保留的最大记录数量 | 10000 |

### 数据管理

#### **导出配置**
- 导出所有配置到JSON文件
- 便于备份和迁移

#### **清空使用记录**
- 清空所有Token使用记录
- 释放存储空间

#### **重置配置**
- 恢复所有配置到默认值
- 谨慎使用此功能

## 🔧 高级功能

### Token使用跟踪

系统会自动跟踪每次分析的Token使用：

```python
# 自动记录的信息
{
    "timestamp": "2025-06-28T10:30:00",
    "provider": "dashscope",
    "model_name": "qwen-plus",
    "input_tokens": 2500,
    "output_tokens": 1200,
    "cost": 0.024,
    "session_id": "analysis_abc123_20250628_1030",
    "analysis_type": "A股_analysis"
}
```

### 成本计算公式

```
总成本 = (输入Token数 / 1000) × 输入单价 + (输出Token数 / 1000) × 输出单价
```

### 会话跟踪

每次分析都会生成唯一的会话ID，用于：
- 跟踪单次分析的完整成本
- 分析不同类型任务的成本差异
- 优化Token使用策略

## 📈 使用场景示例

### 场景1：成本预算管理

**目标**: 控制月度AI使用成本在¥500以内

**操作**:
1. 设置成本警告阈值为¥16.67（500/30天）
2. 选择性价比高的模型（如qwen-turbo）
3. 定期查看使用统计，调整使用频率

### 场景2：多模型对比

**目标**: 比较不同模型的性价比

**操作**:
1. 配置多个模型（qwen-turbo, qwen-plus, gpt-3.5-turbo）
2. 对同一股票进行多次分析
3. 在使用统计中比较成本和效果

### 场景3：团队使用管理

**目标**: 管理团队的AI使用成本

**操作**:
1. 为不同团队成员配置不同的会话ID前缀
2. 通过使用统计分析各成员的使用情况
3. 制定使用规范和预算分配

## ⚠️ 注意事项

### 数据安全
- **API密钥加密存储**: 系统会安全存储您的API密钥
- **本地数据**: 所有配置和使用记录都存储在本地
- **定期备份**: 建议定期导出配置进行备份

### 成本控制
- **实时监控**: 定期查看使用统计
- **合理设置**: 根据需求设置合适的Token限制
- **模型选择**: 选择适合任务的模型，避免过度使用高成本模型

### 故障排除
- **配置丢失**: 使用"重置配置"功能恢复默认设置
- **成本异常**: 检查定价设置是否正确
- **统计错误**: 清空使用记录后重新开始统计

## 🔮 未来功能

### 计划中的功能
- **多货币汇率转换**: 自动转换不同货币的成本
- **成本预测**: 基于历史数据预测未来成本
- **使用报告**: 生成详细的使用报告
- **团队管理**: 支持多用户和权限管理
- **API集成**: 提供配置管理的API接口

---

**通过配置管理系统，您可以更好地控制AI使用成本，优化分析效果！** 💰📊
