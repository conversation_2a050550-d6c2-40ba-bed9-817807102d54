# Config 目录

此目录用于存储TradingAgents的配置文件和使用统计数据。

## 文件说明

- `usage.json` - Token使用统计数据（自动生成）
- `models.json` - 模型配置文件（自动生成）
- `pricing.json` - 定价配置文件（自动生成）
- `settings.json` - 系统设置文件（自动生成）

## 重要说明

⚠️ **数据持久化**：此目录已在Docker Compose中配置为卷挂载，确保容器重启后配置和统计数据不会丢失。

🔒 **安全提醒**：此目录可能包含敏感的使用统计信息，请勿将其提交到公共代码仓库。

## 备份建议

建议定期备份此目录中的重要配置文件，特别是：
- `usage.json` - 包含完整的Token使用历史
- `settings.json` - 包含个人化设置

## 故障排除

如果遇到配置问题：
1. 检查文件权限是否正确
2. 确认Docker卷挂载是否正常
3. 查看应用日志获取详细错误信息