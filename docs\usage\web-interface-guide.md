# TradingAgents-CN Web界面使用指南 (v0.1.4)

## 🌐 概述

TradingAgents-CN 提供了直观易用的Web界面，让您可以通过浏览器轻松进行股票投资分析。v0.1.4版本新增了配置管理、Token统计等功能，本指南将详细介绍Web界面的各项功能和配置选项。

## ✨ v0.1.4 新增功能

- 🎛️ **配置管理**: API密钥管理、模型选择、系统配置
- 💰 **Token统计**: 实时Token使用统计和成本追踪
- 💾 **缓存管理**: 数据缓存状态监控和管理
- 🇨🇳 **A股支持**: 完整的A股股票分析功能

## 🚀 启动Web界面

### 1. 启动命令
```bash
# 启动Web界面
python -m streamlit run web/app.py

# 或者使用简化命令
streamlit run web/app.py
```

### 2. 访问地址
启动后，在浏览器中访问：`http://localhost:8501`

## 📊 界面功能详解

### 分析配置区域

#### 基本设置
- **股票代码**: 输入要分析的股票代码
  - 🇺🇸 **美股**: AAPL, TSLA, NVDA, MSFT
  - 🇨🇳 **A股**: 000001, 600036, 000002, 600519
- **分析日期**: 选择分析的基准日期
- **研究深度**: 选择分析的详细程度（1-5级）

#### 🎯 研究深度详细说明

研究深度是控制分析质量、速度和成本的核心参数。不同级别的具体配置如下：

##### 🚀 1级 - 快速分析 (2-4分钟)
**适用场景**: 日常快速决策、市场概览

**技术配置**:
- **辩论轮次**: 1轮 (最少)
- **AI模型**: qwen-turbo + qwen-plus (最快)
- **记忆功能**: ❌ 禁用 (加速)
- **在线工具**: ❌ 禁用 (使用缓存数据)

**特点**:
- ✅ 速度最快、成本最低
- ✅ 适合频繁查询
- ❌ 分析深度有限
- ❌ 可能错过细节信息

##### 📈 2级 - 基础分析 (4-6分钟)
**适用场景**: 常规投资决策、基础研究

**技术配置**:
- **辩论轮次**: 1轮
- **AI模型**: qwen-plus + qwen-plus (平衡)
- **记忆功能**: ✅ 启用
- **在线工具**: ✅ 启用 (获取最新数据)

**特点**:
- ✅ 速度较快、包含最新数据
- ✅ 成本可控
- ❌ 辩论深度有限

##### 🎯 3级 - 标准分析 (6-10分钟) **[默认推荐]**
**适用场景**: 重要投资决策、标准研究流程

**技术配置**:
- **辩论轮次**: 1轮 (研究员) + 2轮 (风险评估)
- **AI模型**: qwen-plus + qwen-max (质量优先)
- **记忆功能**: ✅ 启用
- **在线工具**: ✅ 启用

**特点**:
- ✅ 平衡速度和质量
- ✅ 风险评估更深入
- ✅ 适合大多数场景
- ❌ 耗时适中

##### 🔬 4级 - 深度分析 (10-15分钟)
**适用场景**: 重大投资决策、详细研究报告

**技术配置**:
- **辩论轮次**: 2轮 (研究员) + 2轮 (风险评估)
- **AI模型**: qwen-plus + qwen-max (高质量)
- **记忆功能**: ✅ 启用
- **在线工具**: ✅ 启用

**特点**:
- ✅ 分析深度高
- ✅ 多轮辩论确保全面性
- ✅ 适合重要决策
- ❌ 耗时较长、成本较高

##### 🏆 5级 - 全面分析 (15-25分钟)
**适用场景**: 最重要的投资决策、完整研究报告

**技术配置**:
- **辩论轮次**: 3轮 (研究员) + 3轮 (风险评估)
- **AI模型**: qwen-max + qwen-max (最高质量)
- **记忆功能**: ✅ 启用
- **在线工具**: ✅ 启用

**特点**:
- ✅ 最全面的分析
- ✅ 最高质量的推理
- ✅ 最可靠的结果
- ❌ 耗时最长、成本最高

#### 📋 研究深度选择建议

| 使用场景 | 推荐级别 | 预期耗时 | 成本 | 适用情况 |
|----------|----------|----------|------|----------|
| 日常市场监控 | 1-2级 | 2-6分钟 | 低 | 快速获取市场概况 |
| 常规投资决策 | 2-3级 | 4-10分钟 | 中低 | 平衡速度和质量 |
| 重要投资决策 | 3-4级 | 6-15分钟 | 中高 | 确保分析质量 |
| 重大资金投入 | 4-5级 | 10-25分钟 | 高 | 最全面的风险评估 |
| 研究报告撰写 | 4-5级 | 10-25分钟 | 高 | 需要详细的分析内容 |

### 分析师团队选择

#### 可选分析师类型
- **📊 市场技术分析师**: 技术指标、价格趋势分析
- **💰 基本面分析师**: 财务数据、公司基本面分析
- **📰 新闻分析师**: 新闻事件、市场动态分析
- **💭 社交媒体分析师**: 市场情绪、投资者态度分析

#### 分析师组合建议
- **最快组合**: 只选择"市场技术分析师" (最快)
- **平衡组合**: "市场技术" + "基本面分析师" (推荐)
- **全面组合**: 选择所有分析师 (最全面但最慢)

### 高级选项

#### 模型配置
- **LLM提供商**: 选择AI模型提供商 (阿里百炼/Google AI)
- **具体模型**: 选择使用的具体AI模型

#### 分析选项
- **包含情绪分析**: 是否包含市场情绪分析
- **包含风险评估**: 是否包含详细风险评估
- **自定义提示**: 添加特定的分析要求

## 📈 分析结果解读

### 核心决策指标
- **投资建议**: 买入/持有/卖出
- **目标价位**: AI预测的合理价格目标
- **置信度**: 对决策的信心程度 (0-1)
- **风险评分**: 投资风险等级 (0-1)

### 详细分析报告
- **🧠 AI分析推理**: 决策的详细逻辑
- **📊 技术分析**: 技术指标和趋势分析
- **💰 基本面分析**: 财务状况和业务分析
- **📰 新闻影响**: 相关新闻事件分析
- **💭 市场情绪**: 投资者情绪和态度
- **⚖️ 风险评估**: 详细的风险分析

## 💡 使用技巧

### 速度优化
1. **快速查看**: 使用1-2级研究深度 + 最少分析师
2. **平衡分析**: 使用2-3级研究深度 + 核心分析师
3. **深度研究**: 使用4-5级研究深度 + 全部分析师

### 成本控制
1. **日常监控**: 使用快速模式，降低API调用成本
2. **重要决策**: 使用标准模式，确保分析质量
3. **关键投资**: 使用深度模式，获得最可靠结果

### 结果验证
1. **多次分析**: 对重要股票进行多次分析对比
2. **不同深度**: 使用不同研究深度验证一致性
3. **历史回测**: 查看历史分析的准确性

## 🔧 故障排除

### 常见问题
1. **分析时间过长**: 降低研究深度或减少分析师数量
2. **目标价位显示N/A**: 检查AI模型配置和API连接
3. **分析失败**: 检查API密钥配置和网络连接

### 性能优化
1. **使用缓存**: 启用数据缓存功能
2. **选择合适模型**: 根据需求选择速度和质量平衡的模型
3. **合理配置**: 根据使用场景选择合适的研究深度

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 查看控制台错误信息
2. 检查API密钥配置
3. 参考FAQ文档
4. 提交Issue到GitHub仓库
