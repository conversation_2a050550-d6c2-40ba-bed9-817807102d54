Stack trace:
Frame         Function      Args
0007FFFFABE0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9AE0) msys-2.0.dll+0x1FE8E
0007FFFFABE0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAEB8) msys-2.0.dll+0x67F9
0007FFFFABE0  000210046832 (000210286019, 0007FFFFAA98, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABE0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABE0  000210068E24 (0007FFFFABF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEC0  00021006A225 (0007FFFFABF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCF4720000 ntdll.dll
7FFCF36B0000 KERNEL32.DLL
7FFCF18F0000 KERNELBASE.dll
7FFCF30F0000 USER32.dll
000210040000 msys-2.0.dll
7FFCF1E30000 win32u.dll
7FFCF32C0000 GDI32.dll
7FFCF1CF0000 gdi32full.dll
7FFCF1E60000 msvcp_win.dll
7FFCF2150000 ucrtbase.dll
7FFCF2BF0000 advapi32.dll
7FFCF4180000 msvcrt.dll
7FFCF45B0000 sechost.dll
7FFCF4330000 RPCRT4.dll
7FFCF0E40000 CRYPTBASE.DLL
7FFCF1850000 bcryptPrimitives.dll
7FFCF3040000 IMM32.DLL
