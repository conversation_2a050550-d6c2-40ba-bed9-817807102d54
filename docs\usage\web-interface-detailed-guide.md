# 🖥️ Web界面详细使用指南

> 📖 **完整指南**: TradingAgents-CN Web界面的详细使用说明和最佳实践

## 🎯 界面概览

TradingAgents-CN Web界面是基于Streamlit构建的现代化股票分析平台，提供直观、专业的用户体验。

### 🏗️ 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    📈 TradingAgents-CN                      │
├─────────────────┬───────────────────────────────────────────┤
│                 │                                           │
│   🤖 模型配置    │            📋 主分析区域                  │
│   ⚙️ 系统设置    │                                           │
│   📊 使用统计    │   📊 分析配置                             │
│   🔑 API管理     │   🔄 实时进度                             │
│                 │   📈 结果展示                             │
│                 │   📤 报告导出                             │
│                 │                                           │
│   侧边栏        │              主内容区                     │
│   (320px)       │                                           │
│                 │                                           │
└─────────────────┴───────────────────────────────────────────┘
```

## 🚀 快速开始

### 1️⃣ 启动应用

#### 方法一: 本地启动
```bash
# 激活虚拟环境
source env/bin/activate  # Linux/Mac
# 或
.\env\Scripts\activate   # Windows

# 启动Web界面
python start_web.py
```

#### 方法二: Docker启动
```bash
# 启动所有服务
docker-compose up -d

# 查看状态
docker-compose ps
```

### 2️⃣ 访问界面
- 打开浏览器访问: `http://localhost:8501`
- 等待界面加载完成（约3-5秒）

## 📋 主要功能区域

### 🤖 侧边栏 - 模型配置

#### LLM提供商选择
- **DashScope (阿里百炼)** ⭐ 推荐中文用户
  - 模型: qwen-turbo, qwen-plus, qwen-max
  - 特点: 中文优化，成本效益高
  
- **DeepSeek V3**
  - 模型: deepseek-chat
  - 特点: 工具调用强，性价比极高
  
- **Google AI**
  - 模型: gemini-2.5-pro, gemini-2.0-flash等
  - 特点: 最新技术，多模态支持
  
- **OpenRouter**
  - 模型: 60+模型聚合
  - 特点: 一个API访问所有主流模型

#### 快速选择按钮
```
[DeepSeek] [Qwen-Plus] [Gemini] [GPT-4o] [Claude]
```
一键切换热门模型，提升操作效率。

#### API密钥管理
- 🔑 **安全输入**: 密钥输入框自动隐藏
- ✅ **状态检查**: 实时验证API密钥有效性
- 💾 **安全存储**: 密钥仅存储在会话中

### 📊 主分析区域

#### 分析配置表单

**市场选择** 🌍
- 美股: 支持NYSE、NASDAQ上市公司
- A股: 支持沪深两市所有股票
- 港股: 支持港交所主板、创业板

**股票代码输入** 📊
- 智能提示: 输入时显示代码格式提示
- 格式验证: 自动验证代码格式正确性
- 历史记录: 记住最近输入的股票代码

**研究深度选择** 🎯
```
●○○○○ 1级 - 快速分析 (2-4分钟)
●●○○○ 2级 - 基础分析 (4-6分钟)  
●●●○○ 3级 - 标准分析 (6-10分钟) ⭐ 推荐
●●●●○ 4级 - 深度分析 (10-15分钟)
●●●●● 5级 - 全面分析 (15-25分钟)
```

**智能体选择** 🤖
- ☑️ 市场技术分析师: 技术指标、图表分析
- ☑️ 基本面分析师: 财务数据、估值分析  
- ☑️ 新闻分析师: 新闻事件、市场情绪
- ☑️ 社交媒体分析师: 社交媒体情绪分析

**分析时间设置** 📅
- 默认: 当前时间
- 历史分析: 支持任意历史时间点
- 时区处理: 自动处理不同市场时区

### 🔄 实时进度跟踪

#### 进度显示组件
```
🔄 正在分析 AAPL (苹果公司)
████████████████████████████████████████████████████░░░░░░
进度: 85% | 预计剩余: 1分30秒

✅ 数据获取完成 (15秒)
✅ 技术分析完成 (45秒)  
✅ 基本面分析完成 (30秒)
✅ 新闻分析完成 (60秒)
🔄 正在进行看涨/看跌辩论... (当前)
⏳ 等待最终决策
```

#### 智能时间预估
- **历史数据**: 基于过往分析时间统计
- **动态调整**: 根据实际进度动态调整预估
- **分阶段显示**: 每个分析阶段独立计时

#### 状态持久化
- **页面刷新**: 刷新页面不丢失分析进度
- **会话恢复**: 自动恢复中断的分析任务
- **错误恢复**: 网络中断后自动重连

### 📈 结果展示

#### 投资决策摘要
```
🎯 投资建议: 买入
📊 置信度: 78%
⚠️ 风险评分: 中等 (6/10)
💰 目标价位: $185.50 (+12.3%)
📅 持有期建议: 3-6个月
```

#### 详细分析报告
- **📈 技术分析**: 图表形态、技术指标、支撑阻力
- **💼 基本面分析**: 财务指标、估值水平、行业对比
- **📰 新闻分析**: 重要新闻、事件影响、市场反应
- **💬 情绪分析**: 市场情绪、机构观点、散户态度

#### 风险提示
- **⚠️ 主要风险**: 识别的关键风险因素
- **🛡️ 风险控制**: 建议的风险管理措施
- **📊 风险评级**: 量化的风险评分

### 📤 报告导出

#### 支持格式
- **📄 Markdown (.md)**: 轻量级，适合技术用户
- **📝 Word (.docx)**: 商务报告，便于编辑
- **📊 PDF (.pdf)**: 正式文档，适合分享

#### 导出内容
- 完整分析报告
- 投资建议摘要
- 风险提示声明
- 分析参数记录

## 💡 使用技巧

### 🎯 最佳实践

#### 模型选择建议
- **日常分析**: DeepSeek V3 (性价比高)
- **重要决策**: Qwen-Plus (平衡性能成本)
- **深度研究**: Gemini-2.5-Pro (最新技术)

#### 研究深度选择
- **快速监控**: 1-2级，适合日常跟踪
- **投资决策**: 3-4级，适合重要投资
- **深度研究**: 5级，适合重大投资决策

#### 时间安排
- **市场开盘前**: 使用前一交易日数据分析
- **盘中分析**: 使用实时数据快速分析
- **收盘后**: 进行深度分析和总结

### ⚡ 快捷操作

#### 键盘快捷键
- **Enter**: 提交分析表单
- **Ctrl+R**: 刷新页面
- **Esc**: 取消当前操作

#### 鼠标操作
- **双击股票代码**: 快速选中代码
- **右键结果区域**: 快速复制内容

### 🔧 故障排除

#### 常见问题

**1. 页面加载缓慢**
- 检查网络连接
- 清除浏览器缓存
- 重启Web服务

**2. API密钥错误**
- 验证密钥格式正确
- 检查密钥权限设置
- 确认账户余额充足

**3. 分析中断**
- 页面刷新恢复进度
- 检查API调用限制
- 查看错误日志信息

**4. 结果显示异常**
- 清除浏览器缓存
- 检查JavaScript是否启用
- 尝试其他浏览器

## 📱 移动端适配

### 响应式设计
- **手机端**: 自动调整布局，侧边栏折叠
- **平板端**: 优化触摸操作体验
- **桌面端**: 完整功能展示

### 移动端使用建议
- 使用横屏模式获得更好体验
- 避免在移动网络下进行深度分析
- 优先使用快速分析模式

## 🔒 安全与隐私

### 数据安全
- **API密钥**: 仅存储在浏览器会话中
- **分析结果**: 可选择本地存储或云端存储
- **用户数据**: 不收集个人敏感信息

### 隐私保护
- **匿名使用**: 无需注册即可使用
- **数据加密**: 传输数据使用HTTPS加密
- **本地处理**: 敏感计算在本地进行

---

> 💡 **提示**: 更多高级功能和配置选项，请参考 [配置指南](../configuration/config-guide.md) 和 [FAQ](../faq/faq.md)。
