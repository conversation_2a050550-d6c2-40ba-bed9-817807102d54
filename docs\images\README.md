# Web界面截图目录

此目录用于存放TradingAgents-CN Web界面的截图文件。

## 📸 需要的截图文件

为了完善README.md中的Web界面展示，请添加以下截图：

### 🏠 主界面截图
- **文件名**: `web-interface-main.png`
- **内容**: 主分析配置界面
- **要求**: 
  - 显示股票代码输入框
  - 显示市场选择（美股/A股/港股）
  - 显示研究深度选择（1-5级）
  - 显示智能体选择选项
  - 显示"开始分析"按钮

### 📊 实时分析进度截图
- **文件名**: `web-interface-progress.png`
- **内容**: 分析进行中的进度显示
- **要求**:
  - 显示进度条和百分比
  - 显示当前分析步骤
  - 显示预计剩余时间
  - 显示已完成的分析阶段

### 📈 分析结果展示截图
- **文件名**: `web-interface-results.png`
- **内容**: 完整的分析结果页面
- **要求**:
  - 显示投资建议（买入/持有/卖出）
  - 显示置信度和风险评分
  - 显示详细分析报告
  - 显示导出按钮

### ⚙️ 模型配置管理截图
- **文件名**: `web-interface-models.png`
- **内容**: 侧边栏的模型配置界面
- **要求**:
  - 显示LLM提供商选择
  - 显示模型选择下拉框
  - 显示快速选择按钮
  - 显示API密钥配置状态

## 📋 截图规范

### 🖼️ 技术要求
- **格式**: PNG格式（推荐）
- **分辨率**: 至少1920x1080
- **质量**: 高清，文字清晰可读
- **大小**: 单个文件不超过2MB

### 🎨 内容要求
- **界面完整**: 显示完整的功能区域
- **数据真实**: 使用真实的股票代码和分析结果
- **状态清晰**: 确保界面状态清晰可见
- **无敏感信息**: 不包含真实的API密钥

### 📱 建议的截图场景
1. **主界面**: 输入"AAPL"或"000001"，选择标准分析
2. **进度界面**: 分析进行到50%左右的状态
3. **结果界面**: 完整的分析报告，包含图表
4. **配置界面**: 显示多个LLM提供商和模型选项

## 🚀 如何获取截图

### 方法1: 启动Web应用
```bash
# 启动Web界面
python start_web.py

# 访问 http://localhost:8501
# 进行股票分析并截图
```

### 方法2: Docker环境
```bash
# 启动Docker服务
docker-compose up -d

# 访问 http://localhost:8501
# 进行分析并截图
```

## 📝 添加截图后的操作

1. 将截图文件放入此目录
2. 确保文件名与README.md中引用的名称一致
3. 检查图片是否正常显示
4. 提交到Git仓库

## 🔄 更新说明

当Web界面有重大更新时，请及时更新对应的截图，确保文档与实际界面保持一致。

---

**注意**: 此目录中的截图将在README.md中展示，代表项目的专业形象，请确保截图质量和内容的专业性。
